"use client";

import React, { useState, useRef, useCallback, useEffect } from 'react';
import {
  BoldIcon,
  ItalicIcon,
  LinkIcon,
  ListBulletIcon,
  CodeBracketIcon,
  PhotoIcon,
  EyeIcon,
  PencilIcon,
  Bars3BottomLeftIcon,
  TableCellsIcon,
  MinusIcon,
  ChatBubbleLeftRightIcon,
  DocumentTextIcon,
  ClockIcon,
  ArrowUturnLeftIcon,
  ArrowUturnRightIcon,
  ChevronDownIcon,
  Cog6ToothIcon,
  ArrowDownTrayIcon,
  UsersIcon
} from '@heroicons/react/24/outline';
import {
  H1Icon,
  H2Icon,
  H3Icon,
  StrikethroughIcon,
  UnderlineIcon,
  QuoteIcon
} from './EditorIcons';
import MarkdownPreview from './MarkdownPreview';
import ImageUploadModal from './ImageUploadModal';
import TableEditorModal from './TableEditorModal';
import CodeBlockModal from './CodeBlockModal';
import VersionHistoryModal from './VersionHistoryModal';
import StatisticsPanel from './StatisticsPanel';
import ToolbarCustomizationModal from './ToolbarCustomizationModal';
import ExportImportModal from './ExportImportModal';
import CollaborationPanel from './CollaborationPanel';

interface ToolbarButton {
  id: string;
  icon: React.ComponentType<{ className?: string }>;
  onClick: () => void;
  title: string;
  group: string;
  enabled: boolean;
  order: number;
  disabled?: boolean;
}

interface RichTextEditorProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  rows?: number;
  className?: string;
  autoSave?: boolean;
  onAutoSave?: (content: string) => void;
  showWordCount?: boolean;
  showReadingTime?: boolean;
  maxLength?: number;
  customToolbar?: boolean;
  enableCollaboration?: boolean;
  documentId?: string;
}

interface LinkModalProps {
  isOpen: boolean;
  onClose: () => void;
  onInsert: (url: string, text: string) => void;
  selectedText?: string;
}

const LinkModal: React.FC<LinkModalProps> = ({ isOpen, onClose, onInsert, selectedText }) => {
  const [url, setUrl] = useState('');
  const [text, setText] = useState(selectedText || '');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (url && text) {
      onInsert(url, text);
      setUrl('');
      setText('');
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
        <h3 className="text-lg font-semibold mb-4">Add Link</h3>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Link Text
            </label>
            <input
              type="text"
              value={text}
              onChange={(e) => setText(e.target.value)}
              placeholder="Enter link text"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              URL
            </label>
            <input
              type="url"
              value={url}
              onChange={(e) => setUrl(e.target.value)}
              placeholder="https://example.com"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              required
            />
          </div>
          <div className="flex justify-end space-x-3">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-gray-600 hover:text-gray-800"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              Insert Link
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export const RichTextEditor: React.FC<RichTextEditorProps> = ({
  value,
  onChange,
  placeholder = "Start writing...",
  rows = 20,
  className = "",
  autoSave = false,
  onAutoSave,
  showWordCount = true,
  showReadingTime = true,
  maxLength,
  customToolbar = true,
  enableCollaboration = false,
  documentId = 'doc-' + Math.random().toString(36).substr(2, 9)
}) => {
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const [showLinkModal, setShowLinkModal] = useState(false);
  const [showImageModal, setShowImageModal] = useState(false);
  const [showTableModal, setShowTableModal] = useState(false);
  const [showCodeBlockModal, setShowCodeBlockModal] = useState(false);
  const [showVersionHistory, setShowVersionHistory] = useState(false);
  const [showStatistics, setShowStatistics] = useState(false);
  const [showToolbarCustomization, setShowToolbarCustomization] = useState(false);
  const [showExportImport, setShowExportImport] = useState(false);
  const [showCollaboration, setShowCollaboration] = useState(false);
  const [selectedText, setSelectedText] = useState('');
  const [isPreviewMode, setIsPreviewMode] = useState(false);
  const [showHeadingDropdown, setShowHeadingDropdown] = useState(false);
  const [history, setHistory] = useState<string[]>([value]);
  const [historyIndex, setHistoryIndex] = useState(0);
  const [wordCount, setWordCount] = useState(0);
  const [charCount, setCharCount] = useState(0);
  const [readingTime, setReadingTime] = useState(0);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);
  const [toolbarConfig, setToolbarConfig] = useState<ToolbarButton[]>([]);

  // Collaboration state
  const [currentUser] = useState({
    id: 'current-user',
    name: 'You',
    color: '#3B82F6',
    status: 'online' as const
  });

  const [collaborators] = useState([
    {
      id: 'user2',
      name: 'John Doe',
      color: '#10B981',
      status: 'online' as const
    },
    {
      id: 'user3',
      name: 'Jane Smith',
      color: '#F59E0B',
      status: 'away' as const
    }
  ]);

  const [comments, setComments] = useState([
    {
      id: 'comment1',
      userId: 'user2',
      content: 'This section needs more detail about the implementation.',
      position: 100,
      timestamp: new Date(Date.now() - 10 * 60000),
      resolved: false
    }
  ]);

  // Calculate statistics
  useEffect(() => {
    const words = value.trim().split(/\s+/).filter(word => word.length > 0);
    setWordCount(words.length);
    setCharCount(value.length);
    setReadingTime(Math.ceil(words.length / 200)); // Average reading speed: 200 words per minute
  }, [value]);

  // Auto-save functionality
  useEffect(() => {
    if (autoSave && onAutoSave && value.trim()) {
      const timer = setTimeout(() => {
        onAutoSave(value);
        setLastSaved(new Date());
      }, 2000); // Auto-save after 2 seconds of inactivity

      return () => clearTimeout(timer);
    }
  }, [value, autoSave, onAutoSave]);

  // History management
  const addToHistory = useCallback((newValue: string) => {
    setHistory(prev => {
      const newHistory = prev.slice(0, historyIndex + 1);
      newHistory.push(newValue);
      return newHistory.slice(-50); // Keep last 50 states
    });
    setHistoryIndex(prev => Math.min(prev + 1, 49));
  }, [historyIndex]);

  const undo = useCallback(() => {
    if (historyIndex > 0) {
      const newIndex = historyIndex - 1;
      setHistoryIndex(newIndex);
      onChange(history[newIndex]);
    }
  }, [historyIndex, history, onChange]);

  const redo = useCallback(() => {
    if (historyIndex < history.length - 1) {
      const newIndex = historyIndex + 1;
      setHistoryIndex(newIndex);
      onChange(history[newIndex]);
    }
  }, [historyIndex, history, onChange]);

  const restoreVersion = useCallback((index: number) => {
    setHistoryIndex(index);
    onChange(history[index]);
  }, [history, onChange]);

  const handleVersionHistory = () => {
    setShowVersionHistory(true);
  };

  const handleStatistics = () => {
    setShowStatistics(true);
  };

  const handleToolbarCustomization = () => {
    setShowToolbarCustomization(true);
  };

  const saveToolbarConfig = (config: ToolbarButton[]) => {
    setToolbarConfig(config);
    // Save to localStorage
    localStorage.setItem('richTextEditor-toolbarConfig', JSON.stringify(config));
  };

  const handleExportImport = () => {
    setShowExportImport(true);
  };

  const handleImport = (importedContent: string) => {
    onChange(importedContent);
    addToHistory(importedContent);
  };

  const handleCollaboration = () => {
    setShowCollaboration(true);
  };

  const handleAddComment = (content: string, position: number) => {
    const newComment = {
      id: 'comment-' + Date.now(),
      userId: currentUser.id,
      content,
      position,
      timestamp: new Date(),
      resolved: false
    };
    setComments(prev => [...prev, newComment]);
  };

  const handleResolveComment = (commentId: string) => {
    setComments(prev => prev.map(comment =>
      comment.id === commentId ? { ...comment, resolved: true } : comment
    ));
  };

  const insertText = (before: string, after: string = '', placeholder: string = '') => {
    const textarea = textareaRef.current;
    if (!textarea) return;

    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const selectedText = value.substring(start, end);
    const textToInsert = selectedText || placeholder;
    
    const newText = value.substring(0, start) + before + textToInsert + after + value.substring(end);
    onChange(newText);

    // Set cursor position
    setTimeout(() => {
      if (selectedText) {
        textarea.setSelectionRange(start + before.length, start + before.length + textToInsert.length);
      } else {
        textarea.setSelectionRange(start + before.length, start + before.length + textToInsert.length);
      }
      textarea.focus();
    }, 0);
  };

  const handleBold = () => insertText('**', '**', 'bold text');
  const handleItalic = () => insertText('*', '*', 'italic text');
  const handleStrikethrough = () => insertText('~~', '~~', 'strikethrough text');
  const handleUnderline = () => insertText('<u>', '</u>', 'underlined text');
  const handleCode = () => insertText('`', '`', 'code');
  const handleCodeBlock = () => {
    setShowCodeBlockModal(true);
  };

  const insertCodeBlock = (codeBlock: string) => {
    const textarea = textareaRef.current;
    if (!textarea) return;

    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;

    const newText = value.substring(0, start) + '\n' + codeBlock + '\n' + value.substring(end);
    onChange(newText);

    // Set cursor position after the code block
    setTimeout(() => {
      const newPosition = start + codeBlock.length + 2;
      textarea.setSelectionRange(newPosition, newPosition);
      textarea.focus();
    }, 0);
  };
  const handleList = () => insertText('\n- ', '', 'list item');
  const handleNumberedList = () => insertText('\n1. ', '', 'numbered item');
  const handleBlockquote = () => insertText('\n> ', '', 'quote');
  const handleHorizontalRule = () => insertText('\n---\n', '', '');

  const handleHeading = (level: number) => {
    const prefix = '#'.repeat(level) + ' ';
    insertText('\n' + prefix, '', `Heading ${level}`);
    setShowHeadingDropdown(false);
  };

  const handleTable = () => {
    setShowTableModal(true);
  };

  const insertTable = (tableMarkdown: string) => {
    const textarea = textareaRef.current;
    if (!textarea) return;

    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;

    const newText = value.substring(0, start) + '\n' + tableMarkdown + '\n' + value.substring(end);
    onChange(newText);

    // Set cursor position after the table
    setTimeout(() => {
      const newPosition = start + tableMarkdown.length + 2;
      textarea.setSelectionRange(newPosition, newPosition);
      textarea.focus();
    }, 0);
  };

  // Keyboard shortcuts
  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (e.ctrlKey || e.metaKey) {
      switch (e.key) {
        case 'b':
          e.preventDefault();
          handleBold();
          break;
        case 'i':
          e.preventDefault();
          handleItalic();
          break;
        case 'k':
          e.preventDefault();
          handleLink();
          break;
        case 'z':
          e.preventDefault();
          if (e.shiftKey) {
            redo();
          } else {
            undo();
          }
          break;
        case 'y':
          e.preventDefault();
          redo();
          break;
        case '`':
          e.preventDefault();
          handleCode();
          break;
        case 'Enter':
          e.preventDefault();
          handleCodeBlock();
          break;
      }
    }
  }, []);

  const handleLink = () => {
    const textarea = textareaRef.current;
    if (!textarea) return;

    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const selected = value.substring(start, end);
    
    setSelectedText(selected);
    setShowLinkModal(true);
  };

  const insertLink = (url: string, text: string) => {
    const textarea = textareaRef.current;
    if (!textarea) return;

    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const linkMarkdown = `[${text}](${url})`;
    
    const newText = value.substring(0, start) + linkMarkdown + value.substring(end);
    onChange(newText);

    // Set cursor position after the link
    setTimeout(() => {
      const newPosition = start + linkMarkdown.length;
      textarea.setSelectionRange(newPosition, newPosition);
      textarea.focus();
    }, 0);
  };

  const insertImage = (url: string, alt: string) => {
    const textarea = textareaRef.current;
    if (!textarea) return;

    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const imageMarkdown = `![${alt}](${url})`;

    const newText = value.substring(0, start) + imageMarkdown + value.substring(end);
    onChange(newText);

    // Set cursor position after the image
    setTimeout(() => {
      const newPosition = start + imageMarkdown.length;
      textarea.setSelectionRange(newPosition, newPosition);
      textarea.focus();
    }, 0);
  };

  const handleImage = () => {
    setShowImageModal(true);
  };

  // Initialize toolbar configuration
  useEffect(() => {
    const defaultConfig: ToolbarButton[] = [
      // History group
      { id: 'undo', icon: ArrowUturnLeftIcon, onClick: undo, title: 'Undo (Ctrl+Z)', group: 'history', enabled: true, order: 0, disabled: historyIndex <= 0 },
      { id: 'redo', icon: ArrowUturnRightIcon, onClick: redo, title: 'Redo (Ctrl+Y)', group: 'history', enabled: true, order: 1, disabled: historyIndex >= history.length - 1 },
      { id: 'version-history', icon: ClockIcon, onClick: handleVersionHistory, title: 'Version History', group: 'history', enabled: true, order: 2, disabled: history.length <= 1 },
      { id: 'statistics', icon: DocumentTextIcon, onClick: handleStatistics, title: 'Document Statistics', group: 'history', enabled: true, order: 3 },

      // Text formatting group
      { id: 'bold', icon: BoldIcon, onClick: handleBold, title: 'Bold (Ctrl+B)', group: 'formatting', enabled: true, order: 4 },
      { id: 'italic', icon: ItalicIcon, onClick: handleItalic, title: 'Italic (Ctrl+I)', group: 'formatting', enabled: true, order: 5 },
      { id: 'strikethrough', icon: StrikethroughIcon, onClick: handleStrikethrough, title: 'Strikethrough', group: 'formatting', enabled: true, order: 6 },
      { id: 'underline', icon: UnderlineIcon, onClick: handleUnderline, title: 'Underline', group: 'formatting', enabled: true, order: 7 },

      // Structure group
      { id: 'code', icon: CodeBracketIcon, onClick: handleCode, title: 'Inline Code (Ctrl+`)', group: 'structure', enabled: true, order: 8 },
      { id: 'code-block', icon: DocumentTextIcon, onClick: handleCodeBlock, title: 'Code Block (Ctrl+Enter)', group: 'structure', enabled: true, order: 9 },
      { id: 'bullet-list', icon: ListBulletIcon, onClick: handleList, title: 'Bullet List', group: 'structure', enabled: true, order: 10 },
      { id: 'numbered-list', icon: Bars3BottomLeftIcon, onClick: handleNumberedList, title: 'Numbered List', group: 'structure', enabled: true, order: 11 },
      { id: 'blockquote', icon: ChatBubbleLeftRightIcon, onClick: handleBlockquote, title: 'Blockquote', group: 'structure', enabled: true, order: 12 },
      { id: 'horizontal-rule', icon: MinusIcon, onClick: handleHorizontalRule, title: 'Horizontal Rule', group: 'structure', enabled: true, order: 13 },

      // Insert group
      { id: 'link', icon: LinkIcon, onClick: handleLink, title: 'Link (Ctrl+K)', group: 'insert', enabled: true, order: 14 },
      { id: 'image', icon: PhotoIcon, onClick: handleImage, title: 'Image', group: 'insert', enabled: true, order: 15 },
      { id: 'table', icon: TableCellsIcon, onClick: handleTable, title: 'Table', group: 'insert', enabled: true, order: 16 },
      { id: 'export-import', icon: ArrowDownTrayIcon, onClick: handleExportImport, title: 'Export & Import', group: 'tools', enabled: true, order: 17 },
      { id: 'collaboration', icon: UsersIcon, onClick: handleCollaboration, title: 'Collaboration', group: 'tools', enabled: enableCollaboration, order: 18 },
    ];

    // Load saved configuration or use default
    const savedConfig = localStorage.getItem('richTextEditor-toolbarConfig');
    if (savedConfig) {
      try {
        const parsed = JSON.parse(savedConfig);
        // Merge with default to ensure all buttons exist
        const mergedConfig = defaultConfig.map(defaultBtn => {
          const savedBtn = parsed.find((btn: ToolbarButton) => btn.id === defaultBtn.id);
          return savedBtn ? { ...defaultBtn, ...savedBtn, onClick: defaultBtn.onClick } : defaultBtn;
        });
        setToolbarConfig(mergedConfig);
      } catch {
        setToolbarConfig(defaultConfig);
      }
    } else {
      setToolbarConfig(defaultConfig);
    }
  }, [historyIndex, history.length]);

  // Group enabled buttons by their group
  const groupedButtons = toolbarConfig
    .filter(btn => btn.enabled)
    .sort((a, b) => a.order - b.order)
    .reduce((groups, button) => {
      if (!groups[button.group]) {
        groups[button.group] = [];
      }
      groups[button.group].push(button);
      return groups;
    }, {} as Record<string, ToolbarButton[]>);

  const togglePreview = () => {
    setIsPreviewMode(!isPreviewMode);
  };

  const HeadingDropdown = () => (
    <div className="relative">
      <button
        type="button"
        onClick={() => setShowHeadingDropdown(!showHeadingDropdown)}
        className="flex items-center space-x-1 px-3 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded transition-colors"
        disabled={isPreviewMode}
      >
        <span className="text-sm font-medium">H</span>
        <ChevronDownIcon className="h-3 w-3" />
      </button>

      {showHeadingDropdown && (
        <div className="absolute top-full left-0 mt-1 bg-white border border-gray-200 rounded-md shadow-lg z-10 min-w-[120px]">
          {[1, 2, 3, 4, 5, 6].map(level => (
            <button
              key={level}
              type="button"
              onClick={() => handleHeading(level)}
              className="w-full text-left px-3 py-2 hover:bg-gray-100 transition-colors"
            >
              <span className={`font-bold text-${level === 1 ? 'xl' : level === 2 ? 'lg' : 'base'}`}>
                H{level}
              </span>
            </button>
          ))}
        </div>
      )}
    </div>
  );

  return (
    <div className={`relative ${className}`}>
      <div className={`border border-gray-300 rounded-lg overflow-hidden ${showCollaboration ? 'mr-80' : ''}`}>
      {/* Toolbar */}
      <div className="bg-gray-50 border-b border-gray-300 px-3 py-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-1">
            {/* Heading Dropdown */}
            <HeadingDropdown />

            {/* Toolbar Groups */}
            {Object.entries(groupedButtons).map(([groupName, buttons], groupIndex) => (
              <div key={groupName} className="flex items-center space-x-1">
                {groupIndex > 0 && <div className="w-px h-6 bg-gray-300 mx-2" />}
                {buttons.map((button) => (
                  <button
                    key={button.id}
                    type="button"
                    onClick={button.onClick}
                    title={button.title}
                    className={`p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded transition-colors ${
                      button.disabled ? 'opacity-50 cursor-not-allowed' : ''
                    }`}
                    disabled={isPreviewMode || button.disabled}
                  >
                    <button.icon className="h-4 w-4" />
                  </button>
                ))}
              </div>
            ))}

            {/* Toolbar Customization Button */}
            {customToolbar && (
              <>
                <div className="w-px h-6 bg-gray-300 mx-2" />
                <button
                  type="button"
                  onClick={handleToolbarCustomization}
                  title="Customize Toolbar"
                  className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded transition-colors"
                >
                  <Cog6ToothIcon className="h-4 w-4" />
                </button>
              </>
            )}
          </div>

          <button
            type="button"
            onClick={togglePreview}
            className={`flex items-center space-x-1 px-3 py-1 rounded transition-colors ${
              isPreviewMode
                ? 'bg-blue-100 text-blue-700'
                : 'text-gray-600 hover:text-gray-900 hover:bg-gray-200'
            }`}
          >
            {isPreviewMode ? (
              <>
                <PencilIcon className="h-4 w-4" />
                <span className="text-sm">Edit</span>
              </>
            ) : (
              <>
                <EyeIcon className="h-4 w-4" />
                <span className="text-sm">Preview</span>
              </>
            )}
          </button>
        </div>
      </div>

      {/* Content Area */}
      {isPreviewMode ? (
        <div className="w-full px-4 py-3 min-h-[400px] bg-white">
          <MarkdownPreview content={value} />
        </div>
      ) : (
        <textarea
          ref={textareaRef}
          value={value}
          onChange={(e) => {
            const newValue = e.target.value;
            if (!maxLength || newValue.length <= maxLength) {
              onChange(newValue);
              addToHistory(newValue);
            }
          }}
          onKeyDown={handleKeyDown}
          placeholder={placeholder}
          rows={rows}
          className="w-full px-4 py-3 border-0 focus:ring-0 focus:outline-none resize-none font-mono text-sm leading-relaxed"
          maxLength={maxLength}
        />
      )}

      {/* Statistics and Help */}
      <div className="bg-gray-50 border-t border-gray-300 px-4 py-2">
        <div className="flex items-center justify-between">
          {/* Statistics */}
          <div className="flex items-center space-x-4 text-xs text-gray-500">
            {showWordCount && (
              <span className="flex items-center">
                <DocumentTextIcon className="h-3 w-3 mr-1" />
                {wordCount} words
              </span>
            )}
            <span>{charCount} characters</span>
            {showReadingTime && (
              <span className="flex items-center">
                <ClockIcon className="h-3 w-3 mr-1" />
                {readingTime} min read
              </span>
            )}
            {maxLength && (
              <span className={charCount > maxLength * 0.9 ? 'text-orange-500' : ''}>
                {charCount}/{maxLength}
              </span>
            )}
            {lastSaved && (
              <span className="text-green-600">
                Saved {lastSaved.toLocaleTimeString()}
              </span>
            )}
          </div>

          {/* Quick Help */}
          <div className="text-xs text-gray-500">
            <details className="relative">
              <summary className="cursor-pointer hover:text-gray-700">Quick Help</summary>
              <div className="absolute right-0 bottom-full mb-2 bg-white border border-gray-200 rounded-lg shadow-lg p-3 w-80 z-20">
                <div className="grid grid-cols-2 gap-2 text-xs">
                  <div>
                    <strong>Formatting:</strong>
                    <div>**Bold** *Italic*</div>
                    <div>~~Strike~~ `Code`</div>
                    <div># H1 ## H2 ### H3</div>
                  </div>
                  <div>
                    <strong>Structure:</strong>
                    <div>- List item</div>
                    <div>1. Numbered</div>
                    <div>&gt; Blockquote</div>
                  </div>
                  <div>
                    <strong>Links & Media:</strong>
                    <div>[Link](url)</div>
                    <div>![Image](url)</div>
                  </div>
                  <div>
                    <strong>Shortcuts:</strong>
                    <div>Ctrl+B Bold</div>
                    <div>Ctrl+I Italic</div>
                    <div>Ctrl+K Link</div>
                  </div>
                </div>
              </div>
            </details>
          </div>
        </div>
      </div>

      {/* Link Modal */}
      <LinkModal
        isOpen={showLinkModal}
        onClose={() => setShowLinkModal(false)}
        onInsert={insertLink}
        selectedText={selectedText}
      />

      {/* Image Upload Modal */}
      <ImageUploadModal
        isOpen={showImageModal}
        onClose={() => setShowImageModal(false)}
        onInsert={insertImage}
      />

      {/* Table Editor Modal */}
      <TableEditorModal
        isOpen={showTableModal}
        onClose={() => setShowTableModal(false)}
        onInsert={insertTable}
      />

      {/* Code Block Modal */}
      <CodeBlockModal
        isOpen={showCodeBlockModal}
        onClose={() => setShowCodeBlockModal(false)}
        onInsert={insertCodeBlock}
      />

      {/* Version History Modal */}
      <VersionHistoryModal
        isOpen={showVersionHistory}
        onClose={() => setShowVersionHistory(false)}
        history={history}
        currentIndex={historyIndex}
        onRestore={restoreVersion}
      />

      {/* Statistics Modal */}
      {showStatistics && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg w-full max-w-4xl max-h-[90vh] overflow-hidden">
            <div className="flex items-center justify-between p-4 border-b border-gray-200">
              <h3 className="text-lg font-semibold">Document Statistics</h3>
              <button
                onClick={() => setShowStatistics(false)}
                className="p-1 hover:bg-gray-100 rounded"
              >
                <XMarkIcon className="h-5 w-5" />
              </button>
            </div>
            <div className="p-4 max-h-[calc(90vh-120px)] overflow-y-auto">
              <StatisticsPanel content={value} isExpanded={true} />
            </div>
          </div>
        </div>
      )}

      {/* Toolbar Customization Modal */}
      <ToolbarCustomizationModal
        isOpen={showToolbarCustomization}
        onClose={() => setShowToolbarCustomization(false)}
        toolbarConfig={toolbarConfig}
        onSave={saveToolbarConfig}
      />

      {/* Export Import Modal */}
      <ExportImportModal
        isOpen={showExportImport}
        onClose={() => setShowExportImport(false)}
        content={value}
        onImport={handleImport}
      />

      {/* Collaboration Panel */}
      {enableCollaboration && (
        <CollaborationPanel
          isOpen={showCollaboration}
          onClose={() => setShowCollaboration(false)}
          currentUser={currentUser}
          collaborators={collaborators}
          comments={comments}
          onAddComment={handleAddComment}
          onResolveComment={handleResolveComment}
          documentId={documentId}
        />
      )}
    </div>
    </div>
  );
};

export default RichTextEditor;
