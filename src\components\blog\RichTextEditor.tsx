"use client";

import React, { useState, useRef, useCallback, useEffect } from 'react';
import {
  BoldIcon,
  ItalicIcon,
  LinkIcon,
  ListBulletIcon,
  CodeBracketIcon,
  PhotoIcon,
  EyeIcon,
  PencilIcon,
  Bars3BottomLeftIcon,
  TableCellsIcon,
  MinusIcon,
  ChatBubbleLeftRightIcon,
  DocumentTextIcon,
  ClockIcon,
  ArrowUturnLeftIcon,
  ArrowUturnRightIcon,
  ChevronDownIcon
} from '@heroicons/react/24/outline';
import {
  H1Icon,
  H2Icon,
  H3Icon,
  StrikethroughIcon,
  UnderlineIcon,
  QuoteIcon
} from './EditorIcons';
import MarkdownPreview from './MarkdownPreview';
import ImageUploadModal from './ImageUploadModal';
import TableEditorModal from './TableEditorModal';
import CodeBlockModal from './CodeBlockModal';
import VersionHistoryModal from './VersionHistoryModal';

interface RichTextEditorProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  rows?: number;
  className?: string;
  autoSave?: boolean;
  onAutoSave?: (content: string) => void;
  showWordCount?: boolean;
  showReadingTime?: boolean;
  maxLength?: number;
}

interface LinkModalProps {
  isOpen: boolean;
  onClose: () => void;
  onInsert: (url: string, text: string) => void;
  selectedText?: string;
}

const LinkModal: React.FC<LinkModalProps> = ({ isOpen, onClose, onInsert, selectedText }) => {
  const [url, setUrl] = useState('');
  const [text, setText] = useState(selectedText || '');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (url && text) {
      onInsert(url, text);
      setUrl('');
      setText('');
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
        <h3 className="text-lg font-semibold mb-4">Add Link</h3>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Link Text
            </label>
            <input
              type="text"
              value={text}
              onChange={(e) => setText(e.target.value)}
              placeholder="Enter link text"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              URL
            </label>
            <input
              type="url"
              value={url}
              onChange={(e) => setUrl(e.target.value)}
              placeholder="https://example.com"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              required
            />
          </div>
          <div className="flex justify-end space-x-3">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-gray-600 hover:text-gray-800"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              Insert Link
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export const RichTextEditor: React.FC<RichTextEditorProps> = ({
  value,
  onChange,
  placeholder = "Start writing...",
  rows = 20,
  className = "",
  autoSave = false,
  onAutoSave,
  showWordCount = true,
  showReadingTime = true,
  maxLength
}) => {
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const [showLinkModal, setShowLinkModal] = useState(false);
  const [showImageModal, setShowImageModal] = useState(false);
  const [showTableModal, setShowTableModal] = useState(false);
  const [showCodeBlockModal, setShowCodeBlockModal] = useState(false);
  const [showVersionHistory, setShowVersionHistory] = useState(false);
  const [selectedText, setSelectedText] = useState('');
  const [isPreviewMode, setIsPreviewMode] = useState(false);
  const [showHeadingDropdown, setShowHeadingDropdown] = useState(false);
  const [history, setHistory] = useState<string[]>([value]);
  const [historyIndex, setHistoryIndex] = useState(0);
  const [wordCount, setWordCount] = useState(0);
  const [charCount, setCharCount] = useState(0);
  const [readingTime, setReadingTime] = useState(0);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);

  // Calculate statistics
  useEffect(() => {
    const words = value.trim().split(/\s+/).filter(word => word.length > 0);
    setWordCount(words.length);
    setCharCount(value.length);
    setReadingTime(Math.ceil(words.length / 200)); // Average reading speed: 200 words per minute
  }, [value]);

  // Auto-save functionality
  useEffect(() => {
    if (autoSave && onAutoSave && value.trim()) {
      const timer = setTimeout(() => {
        onAutoSave(value);
        setLastSaved(new Date());
      }, 2000); // Auto-save after 2 seconds of inactivity

      return () => clearTimeout(timer);
    }
  }, [value, autoSave, onAutoSave]);

  // History management
  const addToHistory = useCallback((newValue: string) => {
    setHistory(prev => {
      const newHistory = prev.slice(0, historyIndex + 1);
      newHistory.push(newValue);
      return newHistory.slice(-50); // Keep last 50 states
    });
    setHistoryIndex(prev => Math.min(prev + 1, 49));
  }, [historyIndex]);

  const undo = useCallback(() => {
    if (historyIndex > 0) {
      const newIndex = historyIndex - 1;
      setHistoryIndex(newIndex);
      onChange(history[newIndex]);
    }
  }, [historyIndex, history, onChange]);

  const redo = useCallback(() => {
    if (historyIndex < history.length - 1) {
      const newIndex = historyIndex + 1;
      setHistoryIndex(newIndex);
      onChange(history[newIndex]);
    }
  }, [historyIndex, history, onChange]);

  const restoreVersion = useCallback((index: number) => {
    setHistoryIndex(index);
    onChange(history[index]);
  }, [history, onChange]);

  const handleVersionHistory = () => {
    setShowVersionHistory(true);
  };

  const insertText = (before: string, after: string = '', placeholder: string = '') => {
    const textarea = textareaRef.current;
    if (!textarea) return;

    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const selectedText = value.substring(start, end);
    const textToInsert = selectedText || placeholder;
    
    const newText = value.substring(0, start) + before + textToInsert + after + value.substring(end);
    onChange(newText);

    // Set cursor position
    setTimeout(() => {
      if (selectedText) {
        textarea.setSelectionRange(start + before.length, start + before.length + textToInsert.length);
      } else {
        textarea.setSelectionRange(start + before.length, start + before.length + textToInsert.length);
      }
      textarea.focus();
    }, 0);
  };

  const handleBold = () => insertText('**', '**', 'bold text');
  const handleItalic = () => insertText('*', '*', 'italic text');
  const handleStrikethrough = () => insertText('~~', '~~', 'strikethrough text');
  const handleUnderline = () => insertText('<u>', '</u>', 'underlined text');
  const handleCode = () => insertText('`', '`', 'code');
  const handleCodeBlock = () => {
    setShowCodeBlockModal(true);
  };

  const insertCodeBlock = (codeBlock: string) => {
    const textarea = textareaRef.current;
    if (!textarea) return;

    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;

    const newText = value.substring(0, start) + '\n' + codeBlock + '\n' + value.substring(end);
    onChange(newText);

    // Set cursor position after the code block
    setTimeout(() => {
      const newPosition = start + codeBlock.length + 2;
      textarea.setSelectionRange(newPosition, newPosition);
      textarea.focus();
    }, 0);
  };
  const handleList = () => insertText('\n- ', '', 'list item');
  const handleNumberedList = () => insertText('\n1. ', '', 'numbered item');
  const handleBlockquote = () => insertText('\n> ', '', 'quote');
  const handleHorizontalRule = () => insertText('\n---\n', '', '');

  const handleHeading = (level: number) => {
    const prefix = '#'.repeat(level) + ' ';
    insertText('\n' + prefix, '', `Heading ${level}`);
    setShowHeadingDropdown(false);
  };

  const handleTable = () => {
    setShowTableModal(true);
  };

  const insertTable = (tableMarkdown: string) => {
    const textarea = textareaRef.current;
    if (!textarea) return;

    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;

    const newText = value.substring(0, start) + '\n' + tableMarkdown + '\n' + value.substring(end);
    onChange(newText);

    // Set cursor position after the table
    setTimeout(() => {
      const newPosition = start + tableMarkdown.length + 2;
      textarea.setSelectionRange(newPosition, newPosition);
      textarea.focus();
    }, 0);
  };

  // Keyboard shortcuts
  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (e.ctrlKey || e.metaKey) {
      switch (e.key) {
        case 'b':
          e.preventDefault();
          handleBold();
          break;
        case 'i':
          e.preventDefault();
          handleItalic();
          break;
        case 'k':
          e.preventDefault();
          handleLink();
          break;
        case 'z':
          e.preventDefault();
          if (e.shiftKey) {
            redo();
          } else {
            undo();
          }
          break;
        case 'y':
          e.preventDefault();
          redo();
          break;
        case '`':
          e.preventDefault();
          handleCode();
          break;
        case 'Enter':
          e.preventDefault();
          handleCodeBlock();
          break;
      }
    }
  }, []);

  const handleLink = () => {
    const textarea = textareaRef.current;
    if (!textarea) return;

    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const selected = value.substring(start, end);
    
    setSelectedText(selected);
    setShowLinkModal(true);
  };

  const insertLink = (url: string, text: string) => {
    const textarea = textareaRef.current;
    if (!textarea) return;

    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const linkMarkdown = `[${text}](${url})`;
    
    const newText = value.substring(0, start) + linkMarkdown + value.substring(end);
    onChange(newText);

    // Set cursor position after the link
    setTimeout(() => {
      const newPosition = start + linkMarkdown.length;
      textarea.setSelectionRange(newPosition, newPosition);
      textarea.focus();
    }, 0);
  };

  const insertImage = (url: string, alt: string) => {
    const textarea = textareaRef.current;
    if (!textarea) return;

    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const imageMarkdown = `![${alt}](${url})`;

    const newText = value.substring(0, start) + imageMarkdown + value.substring(end);
    onChange(newText);

    // Set cursor position after the image
    setTimeout(() => {
      const newPosition = start + imageMarkdown.length;
      textarea.setSelectionRange(newPosition, newPosition);
      textarea.focus();
    }, 0);
  };

  const handleImage = () => {
    setShowImageModal(true);
  };

  const toolbarButtons = [
    {
      type: 'group',
      name: 'History',
      buttons: [
        { icon: ArrowUturnLeftIcon, onClick: undo, title: 'Undo (Ctrl+Z)', disabled: historyIndex <= 0 },
        { icon: ArrowUturnRightIcon, onClick: redo, title: 'Redo (Ctrl+Y)', disabled: historyIndex >= history.length - 1 },
        { icon: ClockIcon, onClick: handleVersionHistory, title: 'Version History', disabled: history.length <= 1 },
      ]
    },
    {
      type: 'group',
      name: 'Text Formatting',
      buttons: [
        { icon: BoldIcon, onClick: handleBold, title: 'Bold (Ctrl+B)', shortcut: '**text**' },
        { icon: ItalicIcon, onClick: handleItalic, title: 'Italic (Ctrl+I)', shortcut: '*text*' },
        { icon: StrikethroughIcon, onClick: handleStrikethrough, title: 'Strikethrough', shortcut: '~~text~~' },
        { icon: UnderlineIcon, onClick: handleUnderline, title: 'Underline', shortcut: '<u>text</u>' },
      ]
    },
    {
      type: 'group',
      name: 'Structure',
      buttons: [
        { icon: CodeBracketIcon, onClick: handleCode, title: 'Inline Code (Ctrl+`)', shortcut: '`code`' },
        { icon: DocumentTextIcon, onClick: handleCodeBlock, title: 'Code Block (Ctrl+Enter)', shortcut: '```code```' },
        { icon: ListBulletIcon, onClick: handleList, title: 'Bullet List', shortcut: '- item' },
        { icon: Bars3BottomLeftIcon, onClick: handleNumberedList, title: 'Numbered List', shortcut: '1. item' },
        { icon: ChatBubbleLeftRightIcon, onClick: handleBlockquote, title: 'Blockquote', shortcut: '> quote' },
        { icon: MinusIcon, onClick: handleHorizontalRule, title: 'Horizontal Rule', shortcut: '---' },
      ]
    },
    {
      type: 'group',
      name: 'Insert',
      buttons: [
        { icon: LinkIcon, onClick: handleLink, title: 'Link (Ctrl+K)', shortcut: '[text](url)' },
        { icon: PhotoIcon, onClick: handleImage, title: 'Image', shortcut: '![alt](url)' },
        { icon: TableCellsIcon, onClick: handleTable, title: 'Table', shortcut: '| col |' },
      ]
    }
  ];

  const togglePreview = () => {
    setIsPreviewMode(!isPreviewMode);
  };

  const HeadingDropdown = () => (
    <div className="relative">
      <button
        type="button"
        onClick={() => setShowHeadingDropdown(!showHeadingDropdown)}
        className="flex items-center space-x-1 px-3 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded transition-colors"
        disabled={isPreviewMode}
      >
        <span className="text-sm font-medium">H</span>
        <ChevronDownIcon className="h-3 w-3" />
      </button>

      {showHeadingDropdown && (
        <div className="absolute top-full left-0 mt-1 bg-white border border-gray-200 rounded-md shadow-lg z-10 min-w-[120px]">
          {[1, 2, 3, 4, 5, 6].map(level => (
            <button
              key={level}
              type="button"
              onClick={() => handleHeading(level)}
              className="w-full text-left px-3 py-2 hover:bg-gray-100 transition-colors"
            >
              <span className={`font-bold text-${level === 1 ? 'xl' : level === 2 ? 'lg' : 'base'}`}>
                H{level}
              </span>
            </button>
          ))}
        </div>
      )}
    </div>
  );

  return (
    <div className={`border border-gray-300 rounded-lg overflow-hidden ${className}`}>
      {/* Toolbar */}
      <div className="bg-gray-50 border-b border-gray-300 px-3 py-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-1">
            {/* Heading Dropdown */}
            <HeadingDropdown />

            {/* Toolbar Groups */}
            {toolbarButtons.map((group, groupIndex) => (
              <div key={groupIndex} className="flex items-center space-x-1">
                {groupIndex > 0 && <div className="w-px h-6 bg-gray-300 mx-2" />}
                {group.buttons.map((button, buttonIndex) => (
                  <button
                    key={buttonIndex}
                    type="button"
                    onClick={button.onClick}
                    title={button.title}
                    className={`p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded transition-colors ${
                      button.disabled ? 'opacity-50 cursor-not-allowed' : ''
                    }`}
                    disabled={isPreviewMode || button.disabled}
                  >
                    <button.icon className="h-4 w-4" />
                  </button>
                ))}
              </div>
            ))}
          </div>

          <button
            type="button"
            onClick={togglePreview}
            className={`flex items-center space-x-1 px-3 py-1 rounded transition-colors ${
              isPreviewMode
                ? 'bg-blue-100 text-blue-700'
                : 'text-gray-600 hover:text-gray-900 hover:bg-gray-200'
            }`}
          >
            {isPreviewMode ? (
              <>
                <PencilIcon className="h-4 w-4" />
                <span className="text-sm">Edit</span>
              </>
            ) : (
              <>
                <EyeIcon className="h-4 w-4" />
                <span className="text-sm">Preview</span>
              </>
            )}
          </button>
        </div>
      </div>

      {/* Content Area */}
      {isPreviewMode ? (
        <div className="w-full px-4 py-3 min-h-[400px] bg-white">
          <MarkdownPreview content={value} />
        </div>
      ) : (
        <textarea
          ref={textareaRef}
          value={value}
          onChange={(e) => {
            const newValue = e.target.value;
            if (!maxLength || newValue.length <= maxLength) {
              onChange(newValue);
              addToHistory(newValue);
            }
          }}
          onKeyDown={handleKeyDown}
          placeholder={placeholder}
          rows={rows}
          className="w-full px-4 py-3 border-0 focus:ring-0 focus:outline-none resize-none font-mono text-sm leading-relaxed"
          maxLength={maxLength}
        />
      )}

      {/* Statistics and Help */}
      <div className="bg-gray-50 border-t border-gray-300 px-4 py-2">
        <div className="flex items-center justify-between">
          {/* Statistics */}
          <div className="flex items-center space-x-4 text-xs text-gray-500">
            {showWordCount && (
              <span className="flex items-center">
                <DocumentTextIcon className="h-3 w-3 mr-1" />
                {wordCount} words
              </span>
            )}
            <span>{charCount} characters</span>
            {showReadingTime && (
              <span className="flex items-center">
                <ClockIcon className="h-3 w-3 mr-1" />
                {readingTime} min read
              </span>
            )}
            {maxLength && (
              <span className={charCount > maxLength * 0.9 ? 'text-orange-500' : ''}>
                {charCount}/{maxLength}
              </span>
            )}
            {lastSaved && (
              <span className="text-green-600">
                Saved {lastSaved.toLocaleTimeString()}
              </span>
            )}
          </div>

          {/* Quick Help */}
          <div className="text-xs text-gray-500">
            <details className="relative">
              <summary className="cursor-pointer hover:text-gray-700">Quick Help</summary>
              <div className="absolute right-0 bottom-full mb-2 bg-white border border-gray-200 rounded-lg shadow-lg p-3 w-80 z-20">
                <div className="grid grid-cols-2 gap-2 text-xs">
                  <div>
                    <strong>Formatting:</strong>
                    <div>**Bold** *Italic*</div>
                    <div>~~Strike~~ `Code`</div>
                    <div># H1 ## H2 ### H3</div>
                  </div>
                  <div>
                    <strong>Structure:</strong>
                    <div>- List item</div>
                    <div>1. Numbered</div>
                    <div>&gt; Blockquote</div>
                  </div>
                  <div>
                    <strong>Links & Media:</strong>
                    <div>[Link](url)</div>
                    <div>![Image](url)</div>
                  </div>
                  <div>
                    <strong>Shortcuts:</strong>
                    <div>Ctrl+B Bold</div>
                    <div>Ctrl+I Italic</div>
                    <div>Ctrl+K Link</div>
                  </div>
                </div>
              </div>
            </details>
          </div>
        </div>
      </div>

      {/* Link Modal */}
      <LinkModal
        isOpen={showLinkModal}
        onClose={() => setShowLinkModal(false)}
        onInsert={insertLink}
        selectedText={selectedText}
      />

      {/* Image Upload Modal */}
      <ImageUploadModal
        isOpen={showImageModal}
        onClose={() => setShowImageModal(false)}
        onInsert={insertImage}
      />

      {/* Table Editor Modal */}
      <TableEditorModal
        isOpen={showTableModal}
        onClose={() => setShowTableModal(false)}
        onInsert={insertTable}
      />

      {/* Code Block Modal */}
      <CodeBlockModal
        isOpen={showCodeBlockModal}
        onClose={() => setShowCodeBlockModal(false)}
        onInsert={insertCodeBlock}
      />

      {/* Version History Modal */}
      <VersionHistoryModal
        isOpen={showVersionHistory}
        onClose={() => setShowVersionHistory(false)}
        history={history}
        currentIndex={historyIndex}
        onRestore={restoreVersion}
      />
    </div>
  );
};

export default RichTextEditor;
