"use client";

import React, { useState, useRef } from 'react';
import {
  BoldIcon,
  ItalicIcon,
  LinkIcon,
  ListBulletIcon,
  CodeBracketIcon,
  PhotoIcon,
  EyeIcon,
  PencilIcon
} from '@heroicons/react/24/outline';
import MarkdownPreview from './MarkdownPreview';

interface RichTextEditorProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  rows?: number;
  className?: string;
}

interface LinkModalProps {
  isOpen: boolean;
  onClose: () => void;
  onInsert: (url: string, text: string) => void;
  selectedText?: string;
}

const LinkModal: React.FC<LinkModalProps> = ({ isOpen, onClose, onInsert, selectedText }) => {
  const [url, setUrl] = useState('');
  const [text, setText] = useState(selectedText || '');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (url && text) {
      onInsert(url, text);
      setUrl('');
      setText('');
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
        <h3 className="text-lg font-semibold mb-4">Add Link</h3>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Link Text
            </label>
            <input
              type="text"
              value={text}
              onChange={(e) => setText(e.target.value)}
              placeholder="Enter link text"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              URL
            </label>
            <input
              type="url"
              value={url}
              onChange={(e) => setUrl(e.target.value)}
              placeholder="https://example.com"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              required
            />
          </div>
          <div className="flex justify-end space-x-3">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-gray-600 hover:text-gray-800"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              Insert Link
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export const RichTextEditor: React.FC<RichTextEditorProps> = ({
  value,
  onChange,
  placeholder = "Start writing...",
  rows = 20,
  className = ""
}) => {
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const [showLinkModal, setShowLinkModal] = useState(false);
  const [selectedText, setSelectedText] = useState('');
  const [isPreviewMode, setIsPreviewMode] = useState(false);

  const insertText = (before: string, after: string = '', placeholder: string = '') => {
    const textarea = textareaRef.current;
    if (!textarea) return;

    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const selectedText = value.substring(start, end);
    const textToInsert = selectedText || placeholder;
    
    const newText = value.substring(0, start) + before + textToInsert + after + value.substring(end);
    onChange(newText);

    // Set cursor position
    setTimeout(() => {
      if (selectedText) {
        textarea.setSelectionRange(start + before.length, start + before.length + textToInsert.length);
      } else {
        textarea.setSelectionRange(start + before.length, start + before.length + textToInsert.length);
      }
      textarea.focus();
    }, 0);
  };

  const handleBold = () => insertText('**', '**', 'bold text');
  const handleItalic = () => insertText('*', '*', 'italic text');
  const handleCode = () => insertText('`', '`', 'code');
  const handleList = () => insertText('\n- ', '', 'list item');

  const handleLink = () => {
    const textarea = textareaRef.current;
    if (!textarea) return;

    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const selected = value.substring(start, end);
    
    setSelectedText(selected);
    setShowLinkModal(true);
  };

  const insertLink = (url: string, text: string) => {
    const textarea = textareaRef.current;
    if (!textarea) return;

    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const linkMarkdown = `[${text}](${url})`;
    
    const newText = value.substring(0, start) + linkMarkdown + value.substring(end);
    onChange(newText);

    // Set cursor position after the link
    setTimeout(() => {
      const newPosition = start + linkMarkdown.length;
      textarea.setSelectionRange(newPosition, newPosition);
      textarea.focus();
    }, 0);
  };

  const toolbarButtons = [
    { icon: BoldIcon, onClick: handleBold, title: 'Bold (Ctrl+B)', shortcut: '**text**' },
    { icon: ItalicIcon, onClick: handleItalic, title: 'Italic (Ctrl+I)', shortcut: '*text*' },
    { icon: CodeBracketIcon, onClick: handleCode, title: 'Code', shortcut: '`code`' },
    { icon: ListBulletIcon, onClick: handleList, title: 'List', shortcut: '- item' },
    { icon: LinkIcon, onClick: handleLink, title: 'Link', shortcut: '[text](url)' },
  ];

  const togglePreview = () => {
    setIsPreviewMode(!isPreviewMode);
  };

  return (
    <div className={`border border-gray-300 rounded-lg overflow-hidden ${className}`}>
      {/* Toolbar */}
      <div className="bg-gray-50 border-b border-gray-300 px-3 py-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-1">
            {toolbarButtons.map((button, index) => (
              <button
                key={index}
                type="button"
                onClick={button.onClick}
                title={button.title}
                className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded transition-colors"
                disabled={isPreviewMode}
              >
                <button.icon className="h-4 w-4" />
              </button>
            ))}
          </div>

          <button
            type="button"
            onClick={togglePreview}
            className={`flex items-center space-x-1 px-3 py-1 rounded transition-colors ${
              isPreviewMode
                ? 'bg-blue-100 text-blue-700'
                : 'text-gray-600 hover:text-gray-900 hover:bg-gray-200'
            }`}
          >
            {isPreviewMode ? (
              <>
                <PencilIcon className="h-4 w-4" />
                <span className="text-sm">Edit</span>
              </>
            ) : (
              <>
                <EyeIcon className="h-4 w-4" />
                <span className="text-sm">Preview</span>
              </>
            )}
          </button>
        </div>
      </div>

      {/* Content Area */}
      {isPreviewMode ? (
        <div className="w-full px-4 py-3 min-h-[400px] bg-white">
          <MarkdownPreview content={value} />
        </div>
      ) : (
        <textarea
          ref={textareaRef}
          value={value}
          onChange={(e) => onChange(e.target.value)}
          placeholder={placeholder}
          rows={rows}
          className="w-full px-4 py-3 border-0 focus:ring-0 focus:outline-none resize-none font-mono text-sm leading-relaxed"
        />
      )}

      {/* Help Text */}
      <div className="bg-gray-50 border-t border-gray-300 px-4 py-2">
        <div className="text-xs text-gray-500">
          <p className="mb-1">Markdown formatting supported:</p>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
            <span># Heading 1</span>
            <span>**Bold text**</span>
            <span>[Link](url)</span>
            <span>## Heading 2</span>
            <span>*Italic text*</span>
            <span>`Code`</span>
            <span>- List item</span>
            <span>![Image](url)</span>
            <span>&gt; Quote</span>
          </div>
        </div>
      </div>

      {/* Link Modal */}
      <LinkModal
        isOpen={showLinkModal}
        onClose={() => setShowLinkModal(false)}
        onInsert={insertLink}
        selectedText={selectedText}
      />
    </div>
  );
};

export default RichTextEditor;
