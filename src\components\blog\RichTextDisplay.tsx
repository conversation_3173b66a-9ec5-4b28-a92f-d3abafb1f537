"use client";

import React, { useRef, useEffect } from 'react';
import { markdownToHtml } from '@/utils/markdownUtils';

interface RichTextDisplayProps {
  content: string;
  onChange: (content: string) => void;
  onSelectionChange?: (start: number, end: number) => void;
  className?: string;
  placeholder?: string;
}

export const RichTextDisplay: React.FC<RichTextDisplayProps> = ({
  content,
  onChange,
  onSelectionChange,
  className = "",
  placeholder = "Start writing..."
}) => {
  const editorRef = useRef<HTMLDivElement>(null);
  const isUpdatingRef = useRef(false);

  // Convert HTML back to markdown (enhanced)
  const htmlToMarkdown = (html: string): string => {
    let markdown = html;

    // Convert links back to markdown - this preserves the link structure
    markdown = markdown.replace(/<a[^>]*href="([^"]*)"[^>]*>([^<]*)<\/a>/g, '[$2]($1)');

    // Convert bold back to markdown
    markdown = markdown.replace(/<strong[^>]*>([^<]*)<\/strong>/g, '**$1**');

    // Convert italic back to markdown
    markdown = markdown.replace(/<em[^>]*>([^<]*)<\/em>/g, '*$1*');

    // Convert strikethrough back to markdown
    markdown = markdown.replace(/<del[^>]*>([^<]*)<\/del>/g, '~~$1~~');

    // Convert underline back to markdown
    markdown = markdown.replace(/<span[^>]*class="[^"]*underline[^"]*"[^>]*>([^<]*)<\/span>/g, '<u>$1</u>');

    // Convert code back to markdown
    markdown = markdown.replace(/<code[^>]*>([^<]*)<\/code>/g, '`$1`');

    // Convert headers back to markdown
    markdown = markdown.replace(/<h1[^>]*>([^<]*)<\/h1>/g, '# $1');
    markdown = markdown.replace(/<h2[^>]*>([^<]*)<\/h2>/g, '## $1');
    markdown = markdown.replace(/<h3[^>]*>([^<]*)<\/h3>/g, '### $1');
    markdown = markdown.replace(/<h4[^>]*>([^<]*)<\/h4>/g, '#### $1');
    markdown = markdown.replace(/<h5[^>]*>([^<]*)<\/h5>/g, '##### $1');
    markdown = markdown.replace(/<h6[^>]*>([^<]*)<\/h6>/g, '###### $1');

    // Convert blockquotes back to markdown
    markdown = markdown.replace(/<blockquote[^>]*>([^<]*)<\/blockquote>/g, '> $1');

    // Convert line breaks and paragraphs
    markdown = markdown.replace(/<br\s*\/?>/g, '\n');
    markdown = markdown.replace(/<\/p>\s*<p[^>]*>/g, '\n\n');
    markdown = markdown.replace(/<\/?p[^>]*>/g, '');
    markdown = markdown.replace(/<\/div>\s*<div[^>]*>/g, '\n\n');
    markdown = markdown.replace(/<\/?div[^>]*>/g, '');

    // Remove other HTML tags but preserve content
    markdown = markdown.replace(/<[^>]*>/g, '');

    // Decode HTML entities
    markdown = markdown.replace(/&lt;/g, '<');
    markdown = markdown.replace(/&gt;/g, '>');
    markdown = markdown.replace(/&amp;/g, '&');
    markdown = markdown.replace(/&nbsp;/g, ' ');

    // Clean up extra whitespace
    markdown = markdown.replace(/\n{3,}/g, '\n\n');

    return markdown.trim();
  };

  // Update editor content when content prop changes
  useEffect(() => {
    if (editorRef.current && !isUpdatingRef.current) {
      const htmlContent = markdownToHtml(content, {
        enableLinks: true,
        enableImages: true,
        enableCodeBlocks: false, // Disable for inline editing
        enableTables: false, // Disable for inline editing
        targetBlank: false, // Don't open links in editor
        className: {
          link: 'text-blue-600 hover:text-blue-800 underline cursor-pointer',
          code: 'bg-gray-100 px-1 py-0.5 rounded text-sm font-mono',
        }
      });
      
      editorRef.current.innerHTML = htmlContent;
    }
  }, [content]);

  // Handle content changes
  const handleInput = () => {
    if (editorRef.current) {
      isUpdatingRef.current = true;
      const htmlContent = editorRef.current.innerHTML;
      const markdownContent = htmlToMarkdown(htmlContent);
      onChange(markdownContent);
      
      // Reset flag after a short delay
      setTimeout(() => {
        isUpdatingRef.current = false;
      }, 100);
    }
  };

  // Handle selection changes
  const handleSelectionChange = () => {
    if (onSelectionChange && editorRef.current) {
      const selection = window.getSelection();
      if (selection && selection.rangeCount > 0) {
        const range = selection.getRangeAt(0);
        const start = range.startOffset;
        const end = range.endOffset;
        onSelectionChange(start, end);
      }
    }
  };

  // Handle keyboard shortcuts
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.ctrlKey || e.metaKey) {
      switch (e.key) {
        case 'b':
          e.preventDefault();
          document.execCommand('bold');
          handleInput();
          break;
        case 'i':
          e.preventDefault();
          document.execCommand('italic');
          handleInput();
          break;
        case 'u':
          e.preventDefault();
          document.execCommand('underline');
          handleInput();
          break;
      }
    }
  };

  // Handle paste events
  const handlePaste = (e: React.ClipboardEvent) => {
    e.preventDefault();
    const text = e.clipboardData.getData('text/plain');
    document.execCommand('insertText', false, text);
    handleInput();
  };

  return (
    <div className={`relative ${className}`}>
      <div
        ref={editorRef}
        contentEditable
        onInput={handleInput}
        onKeyDown={handleKeyDown}
        onPaste={handlePaste}
        onMouseUp={handleSelectionChange}
        onKeyUp={handleSelectionChange}
        className="w-full px-4 py-3 border-0 focus:ring-0 focus:outline-none resize-none min-h-[400px] leading-relaxed"
        style={{
          fontSize: '14px',
          fontFamily: 'ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace'
        }}
        suppressContentEditableWarning={true}
      />
      
      {/* Placeholder */}
      {!content && (
        <div className="absolute top-3 left-4 text-gray-400 pointer-events-none text-sm">
          {placeholder}
        </div>
      )}
      
      {/* Rich text styling */}
      <style jsx>{`
        [contenteditable] {
          outline: none;
        }
        
        [contenteditable] a {
          pointer-events: none;
          text-decoration: underline;
          color: #2563eb;
        }
        
        [contenteditable] strong {
          font-weight: 600;
        }
        
        [contenteditable] em {
          font-style: italic;
        }
        
        [contenteditable] code {
          background-color: #f3f4f6;
          padding: 2px 4px;
          border-radius: 3px;
          font-family: ui-monospace, SFMono-Regular, monospace;
          font-size: 0.875rem;
        }
        
        [contenteditable] h1 {
          font-size: 1.875rem;
          font-weight: 700;
          margin: 1.5rem 0 1rem 0;
        }
        
        [contenteditable] h2 {
          font-size: 1.5rem;
          font-weight: 600;
          margin: 1.25rem 0 0.75rem 0;
        }
        
        [contenteditable] h3 {
          font-size: 1.25rem;
          font-weight: 600;
          margin: 1rem 0 0.5rem 0;
        }
        
        [contenteditable] p {
          margin-bottom: 1rem;
          line-height: 1.6;
        }
      `}</style>
    </div>
  );
};

export default RichTextDisplay;
