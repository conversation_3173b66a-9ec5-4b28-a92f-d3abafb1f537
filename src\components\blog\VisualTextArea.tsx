"use client";

import React, { useRef, useEffect, useState } from 'react';

interface VisualTextAreaProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  rows?: number;
  className?: string;
  onKeyDown?: (e: React.KeyboardEvent) => void;
  maxLength?: number;
}

export const VisualTextArea: React.FC<VisualTextAreaProps> = ({
  value,
  onChange,
  placeholder = "Start writing...",
  rows = 20,
  className = "",
  onKeyDown,
  maxLength
}) => {
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const overlayRef = useRef<HTMLDivElement>(null);
  const [isFocused, setIsFocused] = useState(false);

  // Convert markdown to visual display
  const convertToVisual = (text: string): string => {
    let visual = text;

    // Convert links to just show the text part with better styling
    visual = visual.replace(/\[([^\]]+)\]\([^)]+\)/g, '<span class="text-blue-600 underline cursor-pointer hover:text-blue-800 transition-colors">$1</span>');

    // Convert bold
    visual = visual.replace(/\*\*([^*]+)\*\*/g, '<strong class="font-semibold text-gray-900">$1</strong>');

    // Convert italic
    visual = visual.replace(/\*([^*]+)\*/g, '<em class="italic text-gray-800">$1</em>');

    // Convert strikethrough
    visual = visual.replace(/~~([^~]+)~~/g, '<del class="line-through text-gray-500">$1</del>');

    // Convert underline
    visual = visual.replace(/<u>([^<]+)<\/u>/g, '<span class="underline text-gray-800">$1</span>');

    // Convert inline code
    visual = visual.replace(/`([^`]+)`/g, '<code class="bg-gray-100 px-1 py-0.5 rounded text-sm font-mono text-red-600">$1</code>');

    // Convert headers with better spacing
    visual = visual.replace(/^# (.+)$/gm, '<h1 class="text-2xl font-bold text-gray-900 mt-4 mb-2">$1</h1>');
    visual = visual.replace(/^## (.+)$/gm, '<h2 class="text-xl font-semibold text-gray-800 mt-3 mb-2">$1</h2>');
    visual = visual.replace(/^### (.+)$/gm, '<h3 class="text-lg font-semibold text-gray-700 mt-3 mb-1">$1</h3>');
    visual = visual.replace(/^#### (.+)$/gm, '<h4 class="text-base font-semibold text-gray-700 mt-2 mb-1">$1</h4>');

    // Convert blockquotes
    visual = visual.replace(/^> (.+)$/gm, '<blockquote class="border-l-4 border-blue-400 pl-3 italic text-gray-600 bg-blue-50 py-1">$1</blockquote>');

    // Convert lists
    visual = visual.replace(/^- (.+)$/gm, '<li class="text-gray-700 ml-4">• $1</li>');
    visual = visual.replace(/^\d+\. (.+)$/gm, '<li class="text-gray-700 ml-4">$1</li>');

    // Convert line breaks
    visual = visual.replace(/\n/g, '<br>');

    return visual;
  };

  // Update overlay when value changes
  useEffect(() => {
    if (overlayRef.current && !isFocused) {
      overlayRef.current.innerHTML = convertToVisual(value);
    }
  }, [value, isFocused]);

  // Sync scroll between textarea and overlay
  const handleScroll = () => {
    if (textareaRef.current && overlayRef.current) {
      overlayRef.current.scrollTop = textareaRef.current.scrollTop;
      overlayRef.current.scrollLeft = textareaRef.current.scrollLeft;
    }
  };

  const handleFocus = () => {
    setIsFocused(true);
    if (overlayRef.current) {
      overlayRef.current.style.display = 'none';
    }
  };

  const handleBlur = () => {
    setIsFocused(false);
    if (overlayRef.current) {
      overlayRef.current.style.display = 'block';
      overlayRef.current.innerHTML = convertToVisual(value);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newValue = e.target.value;
    if (!maxLength || newValue.length <= maxLength) {
      onChange(newValue);
    }
  };

  return (
    <div className={`relative ${className}`}>
      {/* Visual overlay */}
      <div
        ref={overlayRef}
        className="absolute inset-0 px-4 py-3 pointer-events-none overflow-hidden whitespace-pre-wrap font-mono text-sm leading-relaxed text-transparent"
        style={{
          fontSize: '14px',
          lineHeight: '1.5',
          fontFamily: 'ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace',
          zIndex: 1
        }}
        onScroll={handleScroll}
      />
      
      {/* Actual textarea */}
      <textarea
        ref={textareaRef}
        value={value}
        onChange={handleChange}
        onKeyDown={onKeyDown}
        onFocus={handleFocus}
        onBlur={handleBlur}
        onScroll={handleScroll}
        placeholder={placeholder}
        rows={rows}
        maxLength={maxLength}
        className={`relative w-full px-4 py-3 border-0 focus:ring-0 focus:outline-none resize-none font-mono text-sm leading-relaxed ${
          isFocused ? 'text-gray-900' : 'text-transparent'
        }`}
        style={{
          zIndex: 2,
          backgroundColor: isFocused ? 'white' : 'transparent'
        }}
      />
      
      {/* Enhanced visual overlay for non-focused state */}
      {!isFocused && (
        <div
          className="absolute inset-0 px-4 py-3 pointer-events-none overflow-hidden whitespace-pre-wrap text-sm leading-relaxed"
          style={{
            fontSize: '14px',
            lineHeight: '1.5',
            zIndex: 3
          }}
          dangerouslySetInnerHTML={{ __html: convertToVisual(value) }}
        />
      )}
      
      {/* Placeholder */}
      {!value && !isFocused && (
        <div className="absolute top-3 left-4 text-gray-400 pointer-events-none text-sm z-4">
          {placeholder}
        </div>
      )}
    </div>
  );
};

export default VisualTextArea;
