"use client";

import React, { useState, useEffect, useRef } from "react";
import { useRouter, useParams } from "next/navigation";
import { useSession } from "next-auth/react";
import { MainLayout } from "@/components/layout/MainLayout";
import { Button } from "@/components/ui/Button";
import { Input } from "@/components/ui/Input";
import { Badge } from "@/components/ui/Badge";
import { Spinner } from "@/components/ui/Spinner";
import {
  ArrowLeftIcon,
  PhotoIcon,
  XMarkIcon,
  EyeIcon,
  PencilIcon,
  DocumentTextIcon,
  ClockIcon,
  TagIcon,
  FolderIcon,
} from "@heroicons/react/24/outline";
import { uploadToCloudinary } from "@/lib/cloudinary";
import RichTextEditor from "@/components/blog/RichTextEditor";
import Image from "next/image";

interface Category {
  id: string;
  name: string;
  color: string;
}

interface Blog {
  id: string;
  title: string;
  slug: string;
  content: string;
  excerpt?: string | null;
  coverImage?: string | null;
  categoryId?: string | null;
  tags?: string[] | null;
  status: "draft" | "published";
  readTime?: number | null;
  featured: boolean;
  seoTitle?: string | null;
  seoDescription?: string | null;
  author: {
    id: string;
    name: string;
  };
}

export default function EditBlogPage() {
  const router = useRouter();
  const params = useParams();
  const { data: session } = useSession();
  const fileInputRef = useRef<HTMLInputElement>(null);

  const [blog, setBlog] = useState<Blog | null>(null);
  const [loading, setLoading] = useState(true);
  const [formData, setFormData] = useState({
    title: "",
    excerpt: "",
    content: "",
    categoryId: "",
    tags: [] as string[],
    coverImage: null as File | null,
    status: "draft" as "draft" | "published",
    requestMonetization: false
  });

  const [coverImagePreview, setCoverImagePreview] = useState<string | null>(null);
  const [newTag, setNewTag] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showPreview, setShowPreview] = useState(false);
  const [wordCount, setWordCount] = useState(0);
  const [readTime, setReadTime] = useState(0);

  const [categories, setCategories] = useState<Category[]>([]);
  const [monetizationEnabled, setMonetizationEnabled] = useState(false);
  const [monetizationConfig, setMonetizationConfig] = useState<any>(null);

  // Fetch blog data
  useEffect(() => {
    const fetchBlog = async () => {
      if (!params?.slug) return;

      try {
        const response = await fetch(`/api/blogs/${params.slug}`);
        if (response.ok) {
          const data = await response.json();
          setBlog(data);

          // Check if user is the author
          if (session?.user?.id !== data.author.id) {
            router.push("/blogs");
            return;
          }

          // Set form data
          setFormData({
            title: data.title,
            excerpt: data.excerpt || "",
            content: data.content,
            categoryId: data.categoryId || "",
            tags: data.tags || [],
            coverImage: null,
            status: data.status,
            requestMonetization: false
          });

          setCoverImagePreview(data.coverImage);
          updateStats(data.content);
        } else {
          router.push("/blogs");
        }
      } catch (error) {
        console.error("Error fetching blog:", error);
        router.push("/blogs");
      } finally {
        setLoading(false);
      }
    };

    if (session?.user) {
      fetchBlog();
    }
  }, [params?.slug, session, router]);

  // Fetch categories
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const response = await fetch("/api/blogs/categories");
        if (response.ok) {
          const data = await response.json();
          setCategories(data);
        }
      } catch (error) {
        console.error("Error fetching categories:", error);
      }
    };

    fetchCategories();
  }, []);

  // Fetch monetization status
  useEffect(() => {
    const fetchMonetizationStatus = async () => {
      try {
        const response = await fetch("/api/monetization/status");
        if (response.ok) {
          const data = await response.json();
          if (data.success) {
            setMonetizationEnabled(data.data.isEnabled);
            setMonetizationConfig(data.data);
          }
        }
      } catch (error) {
        console.error("Error fetching monetization status:", error);
      }
    };

    fetchMonetizationStatus();
  }, []);

  const updateStats = (content: string) => {
    const words = content.trim().split(/\s+/).filter(word => word.length > 0);
    const wordCount = words.length;
    const readTime = Math.max(1, Math.ceil(wordCount / 200)); // Average reading speed: 200 words per minute

    setWordCount(wordCount);
    setReadTime(readTime);
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));

    if (field === "content") {
      updateStats(value);
    }
  };

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setFormData(prev => ({ ...prev, coverImage: file }));

      // Create preview
      const reader = new FileReader();
      reader.onload = (e) => {
        setCoverImagePreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const removeCoverImage = () => {
    setFormData(prev => ({ ...prev, coverImage: null }));
    setCoverImagePreview(blog?.coverImage || null);
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const addTag = () => {
    if (newTag.trim() && !formData.tags.includes(newTag.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, newTag.trim()]
      }));
      setNewTag("");
    }
  };

  const removeTag = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }));
  };

  const handleSubmit = async (status: "draft" | "published") => {
    if (!formData.title.trim() || !formData.content.trim()) {
      alert("Please fill in the title and content.");
      return;
    }

    setIsSubmitting(true);

    try {
      let coverImageUrl = blog?.coverImage;

      // Upload new cover image if one is selected
      if (formData.coverImage) {
        try {
          coverImageUrl = await uploadToCloudinary(formData.coverImage);
        } catch (uploadError) {
          console.error("Error uploading cover image:", uploadError);
          alert("Error uploading cover image. Please try again.");
          setIsSubmitting(false);
          return;
        }
      }

      const response = await fetch(`/api/blogs/${params?.slug}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          title: formData.title,
          content: formData.content,
          excerpt: formData.excerpt,
          coverImage: coverImageUrl,
          categoryId: formData.categoryId || null,
          tags: formData.tags.filter(tag => tag.trim() !== ""),
          status,
          readTime,
          requestMonetization: formData.requestMonetization && status === "published"
        }),
      });

      if (response.ok) {
        router.push(`/blogs/${params?.slug}`);
      } else {
        const errorData = await response.json();
        alert(`Error: ${errorData.message}`);
      }
    } catch (error) {
      console.error("Error updating blog:", error);
      alert("An error occurred while updating the blog.");
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderPreview = () => {
    return (
      <article className="prose prose-lg max-w-none">
        {coverImagePreview && (
          <div className="mb-8">
            <Image
              src={coverImagePreview}
              alt={formData.title}
              width={800}
              height={400}
              className="w-full h-64 object-cover rounded-lg"
            />
          </div>
        )}

        <header className="mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            {formData.title || "Untitled"}
          </h1>

          {formData.excerpt && (
            <p className="text-xl text-gray-600 leading-relaxed">
              {formData.excerpt}
            </p>
          )}

          <div className="flex items-center space-x-4 mt-6 text-sm text-gray-500">
            <div className="flex items-center">
              <ClockIcon className="h-4 w-4 mr-1" />
              <span>{readTime} min read</span>
            </div>
            <div className="flex items-center">
              <DocumentTextIcon className="h-4 w-4 mr-1" />
              <span>{wordCount} words</span>
            </div>
          </div>
        </header>

        <div className="whitespace-pre-wrap">
          {formData.content || "Start writing your story..."}
        </div>

        {formData.tags.length > 0 && (
          <div className="mt-8 pt-8 border-t border-gray-200">
            <div className="flex flex-wrap gap-2">
              {formData.tags.map((tag, index) => (
                <Badge key={index} variant="default">
                  {tag}
                </Badge>
              ))}
            </div>
          </div>
        )}
      </article>
    );
  };

  if (loading) {
    return (
      <MainLayout>
        <div className="flex h-64 items-center justify-center">
          <Spinner size="lg" />
        </div>
      </MainLayout>
    );
  }

  if (!blog) {
    return (
      <MainLayout>
        <div className="flex h-64 flex-col items-center justify-center">
          <p className="text-lg font-medium text-gray-900">Blog not found</p>
          <Button className="mt-4" onClick={() => router.push("/blogs")}>
            Back to News/Blogs
          </Button>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="mx-auto max-w-4xl px-4 py-8 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => router.push(`/blogs/${params?.slug}`)}
            >
              <ArrowLeftIcon className="h-4 w-4 mr-2" />
              Back to Post
            </Button>
            <h1 className="text-2xl font-bold text-gray-900">Edit News/Blog</h1>
          </div>

          <div className="flex items-center space-x-3">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowPreview(!showPreview)}
            >
              {showPreview ? (
                <>
                  <PencilIcon className="h-4 w-4 mr-2" />
                  Edit
                </>
              ) : (
                <>
                  <EyeIcon className="h-4 w-4 mr-2" />
                  Preview
                </>
              )}
            </Button>
          </div>
        </div>

        {showPreview ? (
          <div className="bg-white rounded-lg border border-gray-200 p-8">
            <div className="mb-6 pb-6 border-b border-gray-200">
              <h2 className="text-lg font-semibold text-gray-900 mb-2">Preview</h2>
              <p className="text-sm text-gray-600">
                This is how your updated story will appear to readers
              </p>
            </div>
            {renderPreview()}
          </div>
        ) : (
          <div className="space-y-8">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                Cover Image (Optional)
              </label>

              {coverImagePreview ? (
                <div className="relative">
                  <Image
                    src={coverImagePreview}
                    alt="Cover preview"
                    width={800}
                    height={300}
                    className="w-full h-48 object-cover rounded-lg border border-gray-200"
                  />
                  <button
                    onClick={removeCoverImage}
                    className="absolute top-2 right-2 p-1 bg-red-500 text-white rounded-full hover:bg-red-600 transition-colors"
                  >
                    <XMarkIcon className="h-4 w-4" />
                  </button>
                </div>
              ) : (
                <div
                  onClick={() => fileInputRef.current?.click()}
                  className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-gray-400 transition-colors cursor-pointer"
                >
                  <PhotoIcon className="mx-auto h-12 w-12 text-gray-400" />
                  <p className="mt-2 text-sm text-gray-600">
                    Click to upload a cover image
                  </p>
                  <p className="text-xs text-gray-500">
                    PNG, JPG, GIF up to 10MB
                  </p>
                </div>
              )}

              <input
                ref={fileInputRef}
                type="file"
                accept="image/*"
                onChange={handleImageUpload}
                className="hidden"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Title *
              </label>
              <Input
                type="text"
                placeholder="Enter your story title..."
                value={formData.title}
                onChange={(e) => handleInputChange("title", e.target.value)}
                className="text-lg font-semibold"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Excerpt (Optional)
              </label>
              <textarea
                placeholder="Write a brief description of your story..."
                value={formData.excerpt}
                onChange={(e) => handleInputChange("excerpt", e.target.value)}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 resize-none"
              />
              <p className="mt-1 text-xs text-gray-500">
                This will appear as a preview on the blogs page
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <FolderIcon className="inline h-4 w-4 mr-1" />
                Category (Optional)
              </label>
              <select
                value={formData.categoryId}
                onChange={(e) => handleInputChange("categoryId", e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">Select a category</option>
                {categories.map((category) => (
                  <option key={category.id} value={category.id}>
                    {category.name}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <TagIcon className="inline h-4 w-4 mr-1" />
                Tags (Optional)
              </label>

              <div className="flex flex-wrap gap-2 mb-3">
                {formData.tags.map((tag, index) => (
                  <Badge
                    key={index}
                    variant="default"
                    className="flex items-center space-x-1"
                  >
                    <span>{tag}</span>
                    <button
                      onClick={() => removeTag(tag)}
                      className="ml-1 hover:text-red-500"
                    >
                      <XMarkIcon className="h-3 w-3" />
                    </button>
                  </Badge>
                ))}
              </div>

              <div className="flex space-x-2">
                <Input
                  type="text"
                  placeholder="Add a tag..."
                  value={newTag}
                  onChange={(e) => setNewTag(e.target.value)}
                  onKeyPress={(e) => e.key === "Enter" && (e.preventDefault(), addTag())}
                />
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={addTag}
                >
                  Add
                </Button>
              </div>
            </div>

            <div>
              <div className="flex items-center justify-between mb-2">
                <label className="block text-sm font-medium text-gray-700">
                  Content *
                </label>
                <div className="flex items-center space-x-4 text-xs text-gray-500">
                  <div className="flex items-center">
                    <DocumentTextIcon className="h-4 w-4 mr-1" />
                    <span>{wordCount} words</span>
                  </div>
                  <div className="flex items-center">
                    <ClockIcon className="h-4 w-4 mr-1" />
                    <span>{readTime} min read</span>
                  </div>
                </div>
              </div>

              <RichTextEditor
                value={formData.content}
                onChange={(value) => handleInputChange("content", value)}
                placeholder="Continue your story..."
                rows={20}
                className="focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            {monetizationEnabled ? (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0">
                    <svg className="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                    </svg>
                  </div>
                  <div className="flex-1">
                    <h3 className="text-lg font-medium text-blue-900 mb-2">
                      💰 Request Blog Monetization
                    </h3>
                    <p className="text-sm text-blue-800 mb-4">
                      Enable monetization to earn money from qualified readers. You'll earn ${monetizationConfig?.cprRate || 1} for every 1000 qualified reads (minimum {Math.floor((monetizationConfig?.minReadDuration || 120) / 60)} minutes reading time).
                    </p>

                    <div className="space-y-4">
                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          id="requestMonetization"
                          checked={formData.requestMonetization}
                          onChange={(e) => setFormData(prev => ({ ...prev, requestMonetization: e.target.checked }))}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                        <label htmlFor="requestMonetization" className="ml-2 text-sm font-medium text-blue-900">
                          Request monetization for this blog post
                        </label>
                      </div>

                      {formData.requestMonetization && (
                        <div className="bg-blue-100 rounded-lg p-3">
                          <h4 className="text-sm font-medium text-blue-900 mb-2">Monetization Benefits:</h4>
                          <ul className="text-xs text-blue-800 space-y-1">
                            <li>• Earn money for every 1000 qualified reads</li>
                            <li>• Readers must spend at least 2 minutes reading</li>
                            <li>• Automatic payments to your earning wallet</li>
                            <li>• Admin approval required before monetization starts</li>
                          </ul>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              <div className="bg-gray-50 border border-gray-200 rounded-lg p-6">
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0">
                    <svg className="h-6 w-6 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728" />
                    </svg>
                  </div>
                  <div className="flex-1">
                    <h3 className="text-lg font-medium text-gray-700 mb-2">
                      🚫 Blog Monetization Disabled
                    </h3>
                    <p className="text-sm text-gray-600 mb-4">
                      Blog monetization is currently disabled by the administrator. You cannot request monetization for your blog posts at this time.
                    </p>
                    <div className="bg-gray-100 rounded-lg p-3">
                      <p className="text-xs text-gray-600">
                        Contact the administrator if you have questions about monetization availability.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            )}

            <div className="flex items-center justify-end space-x-3 pt-6 border-t border-gray-200">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleSubmit("draft")}
                disabled={isSubmitting}
              >
                {isSubmitting ? <Spinner size="sm" /> : "Save Draft"}
              </Button>

              <Button
                size="sm"
                onClick={() => handleSubmit("published")}
                disabled={isSubmitting}
              >
                {isSubmitting ? <Spinner size="sm" /> : "Update & Publish"}
              </Button>
            </div>
          </div>
        )}
      </div>
    </MainLayout>
  );
}
