"use client";

import React, { useState } from 'react';
import { RichTextEditor } from '@/components/blog/RichTextEditor';

export default function TestEditorPage() {
  const [content, setContent] = useState(`# Test Blog Post

This is a test to see how links work in the editor.

## Links Test

Here are some test links:
- [Google](https://www.google.com) - This should show only "Google" text
- [GitHub](https://github.com) - This should show only "GitHub" text  

**Bold text** and *italic text* should work.

Try switching between Markdown and Rich modes to see the difference!`);

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">Rich Text Editor Test</h1>
        
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h2 className="text-xl font-semibold text-gray-800 mb-4">
            Test the Visual Mode vs Markdown Mode
          </h2>
          <p className="text-gray-600 mb-6">
            Click the "Visual" button to see formatted text without markdown syntax.
            Click "Markdown" to see the raw markdown. Links should appear as just text in Visual mode!
            When you focus on the text area in Visual mode, it will show markdown for editing.
          </p>
          
          <RichTextEditor
            value={content}
            onChange={setContent}
            placeholder="Start writing..."
            rows={15}
            className="border border-gray-300 rounded-lg"
            showWordCount={true}
            showReadingTime={true}
          />
          
          <div className="mt-6 p-4 bg-gray-100 rounded">
            <h3 className="font-semibold mb-2">Current Content (Raw):</h3>
            <pre className="text-sm text-gray-700 whitespace-pre-wrap">
              {content}
            </pre>
          </div>
        </div>
      </div>
    </div>
  );
}
