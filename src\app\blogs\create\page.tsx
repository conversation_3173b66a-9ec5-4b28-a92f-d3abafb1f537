"use client";

import React, { useState, useRef, useEffect } from "react";
import { useRouter } from "next/navigation";
import Image from "next/image";
import { MainLayout } from "@/components/layout/MainLayout";
import { Button } from "@/components/ui/Button";
import { Input } from "@/components/ui/Input";
import { Badge } from "@/components/ui/Badge";
import { uploadToCloudinary } from "@/lib/cloudinary";
import RichTextEditor from "@/components/blog/RichTextEditor";
import {
  PhotoIcon,
  XMarkIcon,
  EyeIcon,
  DocumentTextIcon,
  TagIcon,
  ClockIcon,
  ArrowLeftIcon,
  PlusIcon
} from "@heroicons/react/24/outline";

interface Category {
  id: string;
  name: string;
  color: string;
}

export default function CreateBlogPage() {
  const router = useRouter();
  const fileInputRef = useRef<HTMLInputElement>(null);

  const [formData, setFormData] = useState({
    title: "",
    excerpt: "",
    content: "",
    categoryId: "",
    tags: [] as string[],
    coverImage: null as File | null,
    status: "draft" as "draft" | "published",
    requestMonetization: false
  });

  const [coverImagePreview, setCoverImagePreview] = useState<string | null>(null);
  const [newTag, setNewTag] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showPreview, setShowPreview] = useState(false);
  const [wordCount, setWordCount] = useState(0);
  const [readTime, setReadTime] = useState(0);
  const [categories, setCategories] = useState<Category[]>([]);
  const [monetizationEnabled, setMonetizationEnabled] = useState(false);
  const [monetizationConfig, setMonetizationConfig] = useState<any>(null);

  // Fetch categories from API
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const response = await fetch("/api/blogs/categories");
        if (response.ok) {
          const data = await response.json();
          setCategories(data);
        }
      } catch (error) {
        console.error("Error fetching categories:", error);
      }
    };
    fetchCategories();
  }, []);

  // Fetch monetization status
  useEffect(() => {
    const fetchMonetizationStatus = async () => {
      try {
        const response = await fetch("/api/monetization/status");
        if (response.ok) {
          const data = await response.json();
          if (data.success) {
            setMonetizationEnabled(data.data.isEnabled);
            setMonetizationConfig(data.data);
          }
        }
      } catch (error) {
        console.error("Error fetching monetization status:", error);
      }
    };
    fetchMonetizationStatus();
  }, []);

  // Calculate word count and read time
  const updateStats = (content: string) => {
    const words = content.trim().split(/\s+/).filter(word => word.length > 0).length;
    setWordCount(words);
    setReadTime(Math.ceil(words / 200));
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (field === "content") {
      updateStats(value);
    }
  };

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setFormData(prev => ({ ...prev, coverImage: file }));
      const reader = new FileReader();
      reader.onload = (e) => {
        setCoverImagePreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const removeCoverImage = () => {
    setFormData(prev => ({ ...prev, coverImage: null }));
    setCoverImagePreview(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const addTag = () => {
    if (newTag.trim() && !formData.tags.includes(newTag.trim().toLowerCase())) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, newTag.trim().toLowerCase()]
      }));
      setNewTag("");
    }
  };

  const removeTag = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }));
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      e.preventDefault();
      addTag();
    }
  };

  const handleSubmit = async (status: "draft" | "published") => {
    if (!formData.title.trim() || !formData.content.trim()) {
      alert("Please fill in the title and content");
      return;
    }

    setIsSubmitting(true);

    try {
      let coverImageUrl = null;
      if (formData.coverImage) {
        try {
          coverImageUrl = await uploadToCloudinary(formData.coverImage);
        } catch (uploadError) {
          console.error("Error uploading cover image:", uploadError);
          alert("Error uploading cover image. Please try again.");
          setIsSubmitting(false);
          return;
        }
      }

      const response = await fetch("/api/blogs", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          title: formData.title,
          content: formData.content,
          excerpt: formData.excerpt,
          coverImage: coverImageUrl,
          categoryId: formData.categoryId,
          tags: formData.tags.filter(tag => tag.trim() !== ""),
          status,
          requestMonetization: formData.requestMonetization && status === "published"
        }),
      });

      if (response.ok) {
        const data = await response.json();
        router.push(`/blogs/${data.slug}`);
      } else {
        const errorData = await response.json();
        alert(errorData.message || "Error submitting blog. Please try again.");
      }
    } catch (error) {
      console.error("Error submitting blog:", error);
      alert("Error submitting blog. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <MainLayout>
      <div className="mx-auto max-w-4xl px-4 py-8 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => router.back()}
            >
              <ArrowLeftIcon className="h-4 w-4 mr-2" />
              Back
            </Button>
            <h1 className="text-2xl font-bold text-gray-900">Write a Story</h1>
          </div>
          <div className="flex items-center space-x-3">
            <Button
              variant="outline"
              onClick={() => setShowPreview(!showPreview)}
            >
              <EyeIcon className="h-4 w-4 mr-2" />
              {showPreview ? "Edit" : "Preview"}
            </Button>
          </div>
        </div>

        {showPreview ? (
          <div className="bg-white rounded-lg border border-gray-200 p-8">
            <div className="mb-6 pb-6 border-b border-gray-200">
              <h2 className="text-lg font-semibold text-gray-900 mb-2">Preview</h2>
              <p className="text-sm text-gray-600">
                This is how your story will appear to readers
              </p>
            </div>
            <div className="prose prose-lg max-w-none">
              {coverImagePreview && (
                <div className="relative h-64 sm:h-80 rounded-2xl overflow-hidden mb-8">
                  <Image
                    src={coverImagePreview}
                    alt="Cover preview"
                    fill
                    className="object-cover"
                  />
                </div>
              )}
              <h1 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-4">
                {formData.title || "Your Blog Title"}
              </h1>
              {formData.excerpt && (
                <p className="text-xl text-gray-600 mb-6 italic">
                  {formData.excerpt}
                </p>
              )}
              <div className="text-gray-700">
                {formData.content.split('\n').map((paragraph, index) => {
                  if (paragraph.trim() === '') {
                    return <br key={index} />;
                  }
                  return (
                    <p key={index} className="mb-4 leading-relaxed">
                      {paragraph}
                    </p>
                  );
                })}
              </div>
              {formData.tags.length > 0 && (
                <div className="mt-8">
                  <div className="flex flex-wrap gap-2">
                    {formData.tags.map((tag, index) => (
                      <span
                        key={index}
                        className="px-3 py-1 bg-gray-100 text-gray-600 text-sm rounded-full"
                      >
                        #{tag}
                      </span>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        ) : (
          <div className="space-y-8">
            {/* Cover Image Upload */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                Cover Image (Optional)
              </label>
              {coverImagePreview ? (
                <div className="relative">
                  <div className="relative h-64 sm:h-80 rounded-lg overflow-hidden">
                    <Image
                      src={coverImagePreview}
                      alt="Cover preview"
                      fill
                      className="object-cover"
                    />
                  </div>
                  <button
                    onClick={removeCoverImage}
                    className="absolute top-2 right-2 p-1 bg-red-500 text-white rounded-full hover:bg-red-600 transition-colors"
                  >
                    <XMarkIcon className="h-4 w-4" />
                  </button>
                </div>
              ) : (
                <div
                  onClick={() => fileInputRef.current?.click()}
                  className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-gray-400 transition-colors cursor-pointer"
                >
                  <PhotoIcon className="mx-auto h-12 w-12 text-gray-400" />
                  <p className="mt-2 text-sm text-gray-600">
                    Click to upload a cover image
                  </p>
                  <p className="text-xs text-gray-500">
                    PNG, JPG, GIF up to 10MB
                  </p>
                </div>
              )}
              <input
                ref={fileInputRef}
                type="file"
                accept="image/*"
                onChange={handleImageUpload}
                className="hidden"
              />
            </div>

            {/* Category Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                Category (Optional)
              </label>
              <select
                value={formData.categoryId}
                onChange={(e) => handleInputChange("categoryId", e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="">Select a category</option>
                {categories.map((category) => (
                  <option key={category.id} value={category.id}>
                    {category.name}
                  </option>
                ))}
              </select>
            </div>

            {/* Title */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                Title *
              </label>
              <Input
                type="text"
                value={formData.title}
                onChange={(e) => handleInputChange("title", e.target.value)}
                placeholder="Enter your story title..."
                className="text-lg font-medium"
                required
              />
            </div>

            {/* Excerpt */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                Excerpt (Optional)
              </label>
              <textarea
                value={formData.excerpt}
                onChange={(e) => handleInputChange("excerpt", e.target.value)}
                placeholder="Write a brief description of your story..."
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
              />
            </div>

            {/* Content */}
            <div>
              <div className="flex items-center justify-between mb-3">
                <label className="block text-sm font-medium text-gray-700">
                  Content *
                </label>
                <div className="flex items-center space-x-4 text-sm text-gray-500">
                  <span className="flex items-center">
                    <DocumentTextIcon className="h-4 w-4 mr-1" />
                    {wordCount} words
                  </span>
                  <span className="flex items-center">
                    <ClockIcon className="h-4 w-4 mr-1" />
                    {readTime} min read
                  </span>
                </div>
              </div>
              <RichTextEditor
                value={formData.content}
                onChange={(value) => handleInputChange("content", value)}
                placeholder="Tell your story..."
                rows={20}
                className="focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            {/* Tags */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                Tags (Optional)
              </label>
              <div className="flex flex-wrap gap-2 mb-3">
                {formData.tags.map((tag, index) => (
                  <span
                    key={index}
                    className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800"
                  >
                    #{tag}
                    <button
                      onClick={() => removeTag(tag)}
                      className="ml-2 text-blue-600 hover:text-blue-800"
                    >
                      <XMarkIcon className="h-3 w-3" />
                    </button>
                  </span>
                ))}
              </div>
              <div className="flex">
                <Input
                  type="text"
                  value={newTag}
                  onChange={(e) => setNewTag(e.target.value)}
                  onKeyPress={handleKeyPress}
                  placeholder="Add a tag..."
                  className="flex-1"
                />
                <Button
                  type="button"
                  onClick={addTag}
                  variant="outline"
                  className="ml-2"
                >
                  <PlusIcon className="h-4 w-4" />
                </Button>
              </div>
            </div>

            {/* Monetization Request Section */}
            {monetizationEnabled && (
              <div className="bg-gradient-to-r from-green-50 to-blue-50 border border-green-200 rounded-lg p-6">
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                      <svg className="w-5 h-5 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                      </svg>
                    </div>
                  </div>
                  <div className="flex-1">
                    <h3 className="text-lg font-medium text-gray-900 mb-2">
                      💰 Monetization Request
                    </h3>
                    <p className="text-sm text-gray-600 mb-4">
                      Request monetization for this blog post. If approved, you'll earn money based on reads.
                    </p>
                    <div className="space-y-3">
                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          id="requestMonetization"
                          checked={formData.requestMonetization}
                          onChange={(e) => setFormData(prev => ({ ...prev, requestMonetization: e.target.checked }))}
                          className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                        />
                        <label htmlFor="requestMonetization" className="ml-2 text-sm font-medium text-gray-700">
                          Request monetization for this blog post
                        </label>
                      </div>
                      {formData.requestMonetization && (
                        <div className="ml-6 space-y-2">
                          <div className="bg-white rounded-lg p-4 border border-gray-200">
                            <h4 className="font-medium text-gray-900 mb-2">Monetization Details:</h4>
                            <ul className="text-sm text-gray-600 space-y-1">
                              <li>• <strong>Rate:</strong> ${monetizationConfig?.cprRate || 1} per 1000 reads</li>
                              <li>• <strong>Minimum payout:</strong> ${monetizationConfig?.minPayout || 10}</li>
                              <li>• <strong>Read duration:</strong> Minimum 2 minutes for unique users</li>
                              <li>• <strong>Status:</strong> Pending admin approval</li>
                            </ul>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Action Buttons - Moved to bottom */}
            <div className="flex items-center justify-end space-x-3 pt-6 border-t border-gray-200">
              <Button
                variant="outline"
                onClick={() => handleSubmit("draft")}
                disabled={isSubmitting}
              >
                {isSubmitting ? "Saving..." : "Save Draft"}
              </Button>
              <Button
                onClick={() => handleSubmit("published")}
                disabled={isSubmitting}
              >
                {isSubmitting ? "Publishing..." : "Publish"}
              </Button>
            </div>
          </div>
        )}
      </div>
    </MainLayout>
  );
}
